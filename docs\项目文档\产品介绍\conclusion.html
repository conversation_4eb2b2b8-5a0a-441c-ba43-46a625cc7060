<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>总结与展望 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      .conclusion-page {
        background: var(--gray-100);
        color: var(--gray-800);
        min-height: 100vh;
      }

      .conclusion-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }

      .conclusion-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image:
          radial-gradient(circle at 50% 50%, rgba(255,255,255,0.2) 2px, transparent 2px);
        background-size: 30px 30px;
        opacity: 0.5;
      }

      .conclusion-content {
        position: relative;
        z-index: 2;
      }

      .conclusion-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }

      .conclusion-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }

      .conclusion-grid {
        display: grid;
        gap: var(--space-6);
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .conclusion-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
      }

      .conclusion-card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-light);
      }

      .conclusion-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-light);
        transform: scaleY(0);
        transition: transform var(--transition-normal);
      }

      .conclusion-card:hover::before {
        transform: scaleY(1);
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-4);
        line-height: 1;
      }

      .card-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 40px;
        text-align: center;
        transition: transform var(--transition-normal);
        vertical-align: middle;
        line-height: 1;
      }

      .conclusion-card:hover .card-icon {
        transform: scale(1.1);
      }

      .card-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
        margin: 0;
        line-height: 1.2;
      }

      .card-body {
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }

      .highlight-text {
        color: var(--primary-light);
        font-weight: 600;
      }

      /* 列表样式 */
      .card-body ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .card-body li {
        margin-bottom: var(--space-2);
        position: relative;
        padding-left: var(--space-5);
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }

      .card-body li:before {
        content: "▶";
        color: var(--primary-light);
        font-size: var(--text-xs);
        position: absolute;
        left: 0;
        top: 2px;
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 响应式适配 */
      @media (max-width: 479px) {
        .conclusion-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }

        .conclusion-card {
          padding: var(--space-4);
        }
      }

      @media (min-width: 480px) and (max-width: 767px) {
        .conclusion-grid {
          grid-template-columns: 1fr;
        }
      }

      @media (min-width: 768px) and (max-width: 1023px) {
        .conclusion-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (min-width: 1024px) {
        .conclusion-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container conclusion-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section conclusion-left">
          <div class="conclusion-content">
            <h1 class="conclusion-title">总结与展望</h1>
            <p class="conclusion-subtitle">
              茂名市地质灾害预警平台作为茂名市民身边的地质安全守护者，将持续为提升公众安全防护能力、优化政府服务效率贡献力量。
            </p>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-section">
          <!-- 核心价值总结框 -->
          <div class="conclusion-card" style="margin-bottom: 2rem; background: var(--primary-light); color: var(--white);">
            <div class="card-body">
              <p style="font-size: var(--text-lg); line-height: 1.6; margin: 0;">
                <strong>核心价值总结：</strong>茂名市地质灾害预警平台通过专业化的地质灾害防治信息服务，为人民群众提供便民化的风险查询服务，为政府部门提供高效的数据管理和预警发布能力，实现了"让地质灾害风险信息触手可及，让安全防护深入人心"的产品愿景。
              </p>
            </div>
          </div>

          <div class="conclusion-grid">

            <!-- 功能扩展规划 -->
            <div class="conclusion-card">
              <div class="card-header">
                <i class="card-icon fas fa-plus-circle"></i>
                <h3 class="card-title">功能扩展规划</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">智能化升级</span>：引入AI技术，提供智能化的风险评估和预警决策支持</li>
                  <li><span class="highlight-text">数据分析增强</span>：增强数据统计分析，为决策提供数据支撑</li>
                  <li><span class="highlight-text">移动应用开发</span>：开发专门的移动应用或者小程序，提供更好的移动端用户体验</li>
                  <li><span class="highlight-text">实时监测集成</span>：集成实时监测设备，提供实时监测数据</li>
                </ul>
              </div>
            </div>

            <!-- 服务拓展方向 -->
            <div class="conclusion-card">
              <div class="card-header">
                <i class="card-icon fas fa-expand-arrows-alt"></i>
                <h3 class="card-title">服务拓展方向</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">区域推广</span>：形成区域性的地质灾害防治网络</li>
                  <li><span class="highlight-text">跨部门协作</span>：与气象、水利、应急等部门建立数据共享和协作机制</li>
                  <li><span class="highlight-text">公众教育</span>：增加地质灾害防护知识普及和公众教育功能</li>
                  <li><span class="highlight-text">社会参与</span>：建立公众参与的地质灾害监测和报告机制</li>
                </ul>
              </div>
            </div>

            <!-- 合作发展机遇 -->
            <div class="conclusion-card">
              <div class="card-header">
                <i class="card-icon fas fa-handshake"></i>
                <h3 class="card-title">合作发展机遇</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">政企合作</span>：与电信运营商深化合作，探索5G、物联网等新技术应用</li>
                  <li><span class="highlight-text">学术合作</span>：与高校科研院所开展技术创新合作，推动地质灾害防治技术研发</li>
                  <li><span class="highlight-text">区域合作</span>：推动地区地质灾害防治合作</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="risk_assessment.html" class="nav-btn" aria-label="上一页">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">上一页</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="ending.html" class="nav-btn" aria-label="结束页">
          <span class="mr-2">结束</span>
          <i class="fas fa-flag-checkered" aria-hidden="true"></i>
        </a>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            window.location.href = 'risk_assessment.html';
            break;
          case 'ArrowRight':
            e.preventDefault();
            window.location.href = 'ending.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
