<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险评估与保障 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      /* 风险评估页面特定样式 */
      .risk-page {
        background: var(--gray-100);
        color: var(--gray-800);
        min-height: 100vh;
      }

      .risk-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }

      .risk-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image:
          radial-gradient(circle at 25% 25%, rgba(255,255,255,0.15) 2px, transparent 2px),
          radial-gradient(circle at 75% 75%, rgba(255,255,255,0.15) 2px, transparent 2px);
        background-size: 50px 50px;
        opacity: 0.5;
      }

      .risk-content {
        position: relative;
        z-index: 2;
      }

      .risk-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }

      .risk-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }

      .risk-grid {
        display: grid;
        gap: var(--space-6);
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .risk-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
      }

      .risk-card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-light);
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-4);
        line-height: 1;
      }

      .card-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 40px;
        text-align: center;
        transition: transform var(--transition-normal);
        vertical-align: middle;
        line-height: 1;
      }

      .card-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
        margin: 0;
        line-height: 1.2;
      }

      .card-body {
        color: var(--gray-700);
        line-height: 1.6;
      }

      .card-body ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .card-body li {
        margin-bottom: var(--space-2);
        position: relative;
        padding-left: var(--space-5);
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }

      .card-body li:before {
        content: "▶";
        color: var(--primary-light);
        font-size: var(--text-xs);
        position: absolute;
        left: 0;
        top: 2px;
      }

      .highlight-text {
        color: var(--primary-light);
        font-weight: 600;
      }

      /* 响应式适配 */
      @media (min-width: 768px) {
        .risk-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (min-width: 1024px) {
        .risk-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container risk-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section risk-left">
          <div class="risk-content">
            <h1 class="risk-title">风险评估与保障</h1>
            <p class="risk-subtitle">
              建立完善的风险管控和保障体系，确保平台安全稳定运行，为项目成功实施提供坚实保障。
            </p>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-section">
          <div class="risk-grid">
            <!-- 安全保障体系 -->
            <div class="risk-card">
              <div class="card-header">
                <i class="card-icon fas fa-shield-alt"></i>
                <h3 class="card-title">安全保障体系</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">网络安全</span>：HTTPS加密传输、防火墙保护、IP白名单控制</li>
                  <li><span class="highlight-text">数据安全</span>：权限控制、加密存储、定期备份</li>
                  <li><span class="highlight-text">应用安全</span>：身份认证、双重验证、异常检测</li>
                </ul>
              </div>
            </div>

            <!-- 质量保障体系 -->
            <div class="risk-card">
              <div class="card-header">
                <i class="card-icon fas fa-check-circle"></i>
                <h3 class="card-title">质量保障体系</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">开发质量</span>：代码规范、代码审查、单元测试</li>
                  <li><span class="highlight-text">测试质量</span>：功能测试、性能测试、安全测试</li>
                  <li><span class="highlight-text">运维质量</span>：系统监控、日志分析、版本管理</li>
                </ul>
              </div>
            </div>

            <!-- 项目管理保障 -->
            <div class="risk-card">
              <div class="card-header">
                <i class="card-icon fas fa-users-cog"></i>
                <h3 class="card-title">项目管理保障</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">组织保障</span>：项目架构、沟通机制、决策机制</li>
                  <li><span class="highlight-text">进度保障</span>：项目计划、里程碑控制、风险预警</li>
                  <li><span class="highlight-text">人员保障</span>：技能培训、知识库、人员备份</li>
                </ul>
              </div>
            </div>

            <!-- 持续改进机制 -->
            <div class="risk-card">
              <div class="card-header">
                <i class="card-icon fas fa-sync-alt"></i>
                <h3 class="card-title">持续改进机制</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">用户反馈</span>：多渠道收集、快速响应、满意度调查</li>
                  <li><span class="highlight-text">性能优化</span>：监控分析、评估优化、容量规划</li>
                  <li><span class="highlight-text">功能迭代</span>：需求收集、优先级评估、敏捷开发</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="value_analysis.html" class="nav-btn" aria-label="上一页">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">上一页</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="conclusion.html" class="nav-btn" aria-label="下一页">
          <span class="mr-2">下一页</span>
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </a>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
          case 'Backspace':
            e.preventDefault();
            window.location.href = 'value_analysis.html';
            break;
          case 'ArrowRight':
          case ' ':
            e.preventDefault();
            window.location.href = 'conclusion.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });

      // 添加触摸滑动支持
      let startX = 0;
      let startY = 0;

      document.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
      }, { passive: true });

      document.addEventListener('touchend', function(e) {
        if (!startX || !startY) return;

        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;

        const diffX = startX - endX;
        const diffY = startY - endY;

        // 检测左滑手势 - 下一页
        if (Math.abs(diffX) > Math.abs(diffY) && diffX > 50) {
          window.location.href = 'conclusion.html';
        }
        // 检测右滑手势 - 上一页
        else if (Math.abs(diffX) > Math.abs(diffY) && diffX < -50) {
          window.location.href = 'value_analysis.html';
        }

        startX = 0;
        startY = 0;
      }, { passive: true });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
